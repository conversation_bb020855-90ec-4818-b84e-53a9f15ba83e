# AppsFlyer Integration

This directory contains the complete AppsFlyer SDK integration for ChatToDesign iOS app, implementing a dual analytics architecture with job separation between Firebase Analytics (user behavior) and AppsFlyer (marketing attribution).

## Architecture Overview

### Dual Analytics Architecture

- **Firebase Analytics** (`behaviorAnalyticsService`): User behavior, product analytics, A/B testing
- **AppsFlyer** (`attributionAnalyticsService`): Marketing attribution, campaign tracking, ROI analysis

### Key Components

1. **AppsFlyerAnalyticsAdapter**: Main adapter implementing `AnalyticsService` protocol
2. **AppsFlyerConfiguration**: Configuration model with validation
3. **AppsFlyerEventMapper**: Event and parameter mapping utilities
4. **AppsFlyerDeepLinkHandler**: Deep link processing and routing
5. **AppsFlyerUtils**: Utility functions and helpers

## Setup Instructions

### 1. Configure Remote Config

Add your AppsFlyer credentials to Firebase Remote Config:

```
appsflyer_dev_key: "YOUR_APPSFLYER_DEV_KEY"
appsflyer_apple_app_id: "YOUR_APPLE_APP_ID"
appsflyer_debug_enabled: true (for development)
```

### 2. Update Info.plist

Add ATT usage description:

```xml
<key>NSUserTrackingUsageDescription</key>
<string>This app uses tracking to provide personalized ads and measure ad performance.</string>
```

### 3. Configure SKAN (iOS 15+)

Add SKAN endpoint to Info.plist:

```xml
<key>NSAdvertisingAttributionReportEndpoint</key>
<string>https://appsflyer-skadnetwork.com/</string>
```

## Usage Examples

### Basic Event Tracking

```swift
let container = AppDependencyContainer.shared

// User behavior → Firebase Analytics
container.behaviorAnalyticsService.track(
    name: "screen_viewed",
    parameters: ["screen_name": "chat"],
    category: .userBehavior
)

// Marketing attribution → AppsFlyer
container.attributionAnalyticsService.track(
    name: "campaign_click",
    parameters: ["campaign": "summer_2024"],
    category: .marketing
)
```

### Smart Event Routing

```swift
// Automatically routes to appropriate service based on category
container.trackEvent(MarketingAnalyticsEvent.campaignClick(
    campaign: "holiday_sale",
    mediaSource: "facebook"
))
```

### Deep Link Handling

```swift
// Listen for deep link routing
NotificationCenter.default.addObserver(
    forName: .deepLinkRouteToChat,
    object: nil,
    queue: .main
) { notification in
    // Handle navigation
}
```

### Revenue Tracking

```swift
container.attributionAnalyticsService.trackConversion(
    eventName: "purchase",
    value: 9.99,
    currency: "USD",
    parameters: ["product_id": "pro_monthly"]
)
```

## Event Categories

### AppsFlyer Events (Marketing Attribution)
- Campaign clicks and impressions
- Deep link opens
- Install attribution
- Purchase conversions
- Revenue tracking
- Re-engagement campaigns

### Firebase Analytics Events (User Behavior)
- Screen views and navigation
- Feature usage
- User interactions
- Performance metrics
- A/B test events
- Error tracking

### Dual Platform Events
- User registration/login
- Critical conversions
- Subscription purchases

## Deep Link Configuration

### URL Scheme Setup

Configure your app's URL schemes in Info.plist:

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>com.yourapp.deeplink</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>yourapp</string>
        </array>
    </dict>
</array>
```

### Deep Link Routing

The system supports automatic routing based on deep link values:

- `chat` → Navigate to chat screen
- `design` → Navigate to design screen
- `subscription` → Show paywall
- `profile` → Navigate to profile
- `onboarding` → Show tutorial

## Privacy Compliance

### ATT (App Tracking Transparency)

The integration automatically handles ATT requests:

```swift
// ATT is requested in AppDelegate.applicationDidBecomeActive
// Status changes are tracked for analytics
```

### GDPR Compliance

Configure privacy settings:

```swift
let config = AppsFlyerConfiguration(
    devKey: "your_key",
    appleAppID: "your_id",
    disableIDFACollection: true, // For privacy compliance
    anonymousUserMode: true      // For anonymous tracking
)
```

## Testing

### Debug Mode

Enable debug logging:

```swift
let config = AppsFlyerConfiguration.development(
    devKey: "your_key",
    appleAppID: "your_id"
)
```

### Integration Testing

1. Check AppsFlyer dashboard for events
2. Verify deep link routing
3. Test ATT flow
4. Validate revenue tracking

## Error Handling

The integration includes comprehensive error handling:

- Configuration validation
- Network error recovery
- Graceful degradation
- Detailed logging

## Performance Considerations

- Events are batched automatically
- Minimal impact on app startup
- Efficient memory usage
- Background processing

## Troubleshooting

### Common Issues

1. **Events not appearing**: Check dev key and app ID
2. **Deep links not working**: Verify URL scheme configuration
3. **ATT not showing**: Ensure iOS 14+ and proper timing
4. **Revenue not tracking**: Validate currency and amount formats

### Debug Logs

Enable debug mode to see detailed logs:

```
AppsFlyerAnalyticsAdapter: Event tracked - campaign_click
AppsFlyerDeepLinkHandler: Deep link found - {...}
```

## Migration Guide

If migrating from a single analytics solution:

1. Keep existing Firebase Analytics calls
2. Add AppsFlyer for marketing events
3. Use smart routing for automatic distribution
4. Gradually migrate to dual architecture

## Best Practices

1. **Event Naming**: Use consistent, descriptive names
2. **Parameter Validation**: Always validate event parameters
3. **Privacy First**: Respect user privacy choices
4. **Testing**: Test all deep link scenarios
5. **Monitoring**: Monitor both platforms for data consistency

## Support

For issues or questions:

1. Check AppsFlyer documentation
2. Review integration logs
3. Test with AppsFlyer's debug tools
4. Contact AppsFlyer support if needed
