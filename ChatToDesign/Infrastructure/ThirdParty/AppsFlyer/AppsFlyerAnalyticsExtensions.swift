//
//  AppsFlyerAnalyticsExtensions.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/1/21.
//

import Foundation

// MARK: - AnalyticsEventCategory Extension

public extension AnalyticsEventCategory {
    /// Marketing attribution events
    static let marketing = AnalyticsEventCategory(rawValue: "marketing")
    
    /// Conversion events
    static let conversion = AnalyticsEventCategory(rawValue: "conversion")
    
    /// Revenue events
    static let revenue = AnalyticsEventCategory(rawValue: "revenue")
    
    /// Privacy events
    static let privacy = AnalyticsEventCategory(rawValue: "privacy")
    
    /// Deep link events
    static let deepLink = AnalyticsEventCategory(rawValue: "deep_link")
}

// MARK: - Marketing Events

/// Marketing-specific analytics events for AppsFlyer
public enum MarketingAnalyticsEvent: AnalyticsEvent {
    case campaignClick(campaign: String, mediaSource: String)
    case adView(adId: String, campaign: String?)
    case adClick(adId: String, campaign: String?)
    case deepLinkOpened(url: String, campaign: String?)
    case installAttributed(mediaSource: String, campaign: String?)
    case firstOpen(isOrganic: Bool)
    case reengagement(campaign: String, mediaSource: String)
    
    public var name: String {
        switch self {
        case .campaignClick: return "campaign_click"
        case .adView: return "ad_view"
        case .adClick: return "ad_click"
        case .deepLinkOpened: return "deep_link_opened"
        case .installAttributed: return "install_attributed"
        case .firstOpen: return "first_open"
        case .reengagement: return "reengagement"
        }
    }
    
    public var parameters: [String: Any] {
        switch self {
        case .campaignClick(let campaign, let mediaSource):
            return [
                "campaign": campaign,
                "media_source": mediaSource
            ]
        case .adView(let adId, let campaign):
            var params: [String: Any] = ["ad_id": adId]
            if let campaign = campaign { params["campaign"] = campaign }
            return params
        case .adClick(let adId, let campaign):
            var params: [String: Any] = ["ad_id": adId]
            if let campaign = campaign { params["campaign"] = campaign }
            return params
        case .deepLinkOpened(let url, let campaign):
            var params: [String: Any] = ["url": url]
            if let campaign = campaign { params["campaign"] = campaign }
            return params
        case .installAttributed(let mediaSource, let campaign):
            return [
                "media_source": mediaSource,
                "campaign": campaign
            ]
        case .firstOpen(let isOrganic):
            return ["is_organic": isOrganic]
        case .reengagement(let campaign, let mediaSource):
            return [
                "campaign": campaign,
                "media_source": mediaSource
            ]
        }
    }
    
    public var category: AnalyticsEventCategory {
        return .marketing
    }
    
    public var timestamp: Date {
        return Date()
    }
}

// MARK: - Conversion Events

/// Conversion-specific analytics events for AppsFlyer
public enum ConversionAnalyticsEvent: AnalyticsEvent {
    case userRegistration(method: String, success: Bool)
    case subscriptionStarted(productId: String, price: Double, currency: String)
    case subscriptionCancelled(productId: String, reason: String?)
    case purchaseCompleted(productId: String, price: Double, currency: String, transactionId: String)
    case trialStarted(productId: String)
    case trialConverted(productId: String)
    case levelAchieved(level: Int, score: Int?)
    case tutorialCompleted(step: String)
    
    public var name: String {
        switch self {
        case .userRegistration: return "user_registration"
        case .subscriptionStarted: return "subscription_started"
        case .subscriptionCancelled: return "subscription_cancelled"
        case .purchaseCompleted: return "purchase_completed"
        case .trialStarted: return "trial_started"
        case .trialConverted: return "trial_converted"
        case .levelAchieved: return "level_achieved"
        case .tutorialCompleted: return "tutorial_completed"
        }
    }
    
    public var parameters: [String: Any] {
        switch self {
        case .userRegistration(let method, let success):
            return [
                "method": method,
                "success": success
            ]
        case .subscriptionStarted(let productId, let price, let currency):
            return [
                "product_id": productId,
                "price": price,
                "currency": currency
            ]
        case .subscriptionCancelled(let productId, let reason):
            var params: [String: Any] = ["product_id": productId]
            if let reason = reason { params["reason"] = reason }
            return params
        case .purchaseCompleted(let productId, let price, let currency, let transactionId):
            return [
                "product_id": productId,
                "price": price,
                "currency": currency,
                "transaction_id": transactionId
            ]
        case .trialStarted(let productId):
            return ["product_id": productId]
        case .trialConverted(let productId):
            return ["product_id": productId]
        case .levelAchieved(let level, let score):
            var params: [String: Any] = ["level": level]
            if let score = score { params["score"] = score }
            return params
        case .tutorialCompleted(let step):
            return ["step": step]
        }
    }
    
    public var category: AnalyticsEventCategory {
        return .conversion
    }
    
    public var timestamp: Date {
        return Date()
    }
}

// MARK: - Privacy Events

/// Privacy-specific analytics events
public enum PrivacyAnalyticsEvent: AnalyticsEvent {
    case attStatusChanged(status: String)
    case privacyPolicyAccepted
    case privacyPolicyDeclined
    case dataCollectionOptIn
    case dataCollectionOptOut
    case cookieConsentGiven
    case cookieConsentDenied
    
    public var name: String {
        switch self {
        case .attStatusChanged: return "att_status_changed"
        case .privacyPolicyAccepted: return "privacy_policy_accepted"
        case .privacyPolicyDeclined: return "privacy_policy_declined"
        case .dataCollectionOptIn: return "data_collection_opt_in"
        case .dataCollectionOptOut: return "data_collection_opt_out"
        case .cookieConsentGiven: return "cookie_consent_given"
        case .cookieConsentDenied: return "cookie_consent_denied"
        }
    }
    
    public var parameters: [String: Any] {
        switch self {
        case .attStatusChanged(let status):
            return ["status": status]
        case .privacyPolicyAccepted,
             .privacyPolicyDeclined,
             .dataCollectionOptIn,
             .dataCollectionOptOut,
             .cookieConsentGiven,
             .cookieConsentDenied:
            return [:]
        }
    }
    
    public var category: AnalyticsEventCategory {
        return .privacy
    }
    
    public var timestamp: Date {
        return Date()
    }
}

// MARK: - Convenience Extensions

public extension AnalyticsService {
    
    /// Track marketing event
    /// - Parameter event: Marketing event
    func trackMarketing(_ event: MarketingAnalyticsEvent) {
        track(event: event)
    }
    
    /// Track conversion event
    /// - Parameter event: Conversion event
    func trackConversion(_ event: ConversionAnalyticsEvent) {
        track(event: event)
    }
    
    /// Track privacy event
    /// - Parameter event: Privacy event
    func trackPrivacy(_ event: PrivacyAnalyticsEvent) {
        track(event: event)
    }
    
    /// Track campaign click
    /// - Parameters:
    ///   - campaign: Campaign name
    ///   - mediaSource: Media source
    func trackCampaignClick(campaign: String, mediaSource: String) {
        trackMarketing(.campaignClick(campaign: campaign, mediaSource: mediaSource))
    }
    
    /// Track deep link opened
    /// - Parameters:
    ///   - url: Deep link URL
    ///   - campaign: Campaign name (optional)
    func trackDeepLinkOpened(url: String, campaign: String? = nil) {
        trackMarketing(.deepLinkOpened(url: url, campaign: campaign))
    }
    
    /// Track subscription started
    /// - Parameters:
    ///   - productId: Product identifier
    ///   - price: Subscription price
    ///   - currency: Currency code
    func trackSubscriptionStarted(productId: String, price: Double, currency: String) {
        trackConversion(.subscriptionStarted(productId: productId, price: price, currency: currency))
    }
    
    /// Track ATT status change
    /// - Parameter status: ATT authorization status
    func trackATTStatusChanged(status: String) {
        trackPrivacy(.attStatusChanged(status: status))
    }
}

// MARK: - AppDependencyContainer Extension

public extension AppDependencyContainer {
    
    /// Track event to appropriate analytics service based on category
    /// - Parameter event: Analytics event
    func trackEvent(_ event: AnalyticsEvent) {
        switch event.category {
        case .marketing, .conversion, .revenue, .deepLink:
            // Marketing-related events go to AppsFlyer
            attributionAnalyticsService.track(event: event)
        case .userBehavior, .performance, .error, .privacy:
            // Behavior-related events go to Firebase Analytics
            behaviorAnalyticsService.track(event: event)
        default:
            // Default to behavior analytics
            behaviorAnalyticsService.track(event: event)
        }
    }
    
    /// Track event to both analytics services
    /// - Parameter event: Analytics event
    func trackEventToBoth(_ event: AnalyticsEvent) {
        behaviorAnalyticsService.track(event: event)
        attributionAnalyticsService.track(event: event)
    }
}
